#!/usr/bin/env python3
"""
数据库迁移脚本
添加新的字段到 accounts 表
"""

import logging
from sqlalchemy import text
from app import create_app
from models import db

def migrate_database():
    """执行数据库迁移"""
    app = create_app()
    
    with app.app_context():
        try:
            # 检查是否需要添加新字段
            inspector = db.inspect(db.engine)
            columns = [col['name'] for col in inspector.get_columns('accounts')]
            
            print("当前 accounts 表的字段:")
            for col in columns:
                print(f"  - {col}")
            
            # 需要添加的新字段
            new_columns = [
                ('usage_limit', 'VARCHAR(50)'),
                ('account_type', 'VARCHAR(20) DEFAULT "free"'),
                ('status', 'VARCHAR(20) DEFAULT "active"')
            ]
            
            # 检查并添加缺失的字段
            for col_name, col_definition in new_columns:
                if col_name not in columns:
                    try:
                        sql = f"ALTER TABLE accounts ADD COLUMN {col_name} {col_definition}"
                        print(f"执行 SQL: {sql}")
                        db.session.execute(text(sql))
                        db.session.commit()
                        print(f"✅ 成功添加字段: {col_name}")
                    except Exception as e:
                        print(f"❌ 添加字段 {col_name} 失败: {e}")
                        db.session.rollback()
                else:
                    print(f"⏭️  字段 {col_name} 已存在，跳过")
            
            print("\n数据库迁移完成！")
            
            # 验证新字段
            print("\n验证迁移结果:")
            updated_columns = [col['name'] for col in inspector.get_columns('accounts')]
            for col in updated_columns:
                print(f"  - {col}")
                
        except Exception as e:
            print(f"数据库迁移失败: {e}")
            db.session.rollback()

if __name__ == '__main__':
    migrate_database()
