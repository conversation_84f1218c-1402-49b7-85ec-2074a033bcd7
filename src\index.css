body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f0f2f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* 全局样式 */
.logo {
  height: 32px;
  margin: 16px;
  background: rgba(255, 255, 255, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-weight: bold;
  font-size: 18px;
}

.site-layout-background {
  background: #fff;
}

/* 表格状态样式 */
.expired {
  color: #ff4d4f;
}

.used {
  color: #1890ff;
}

.available {
  color: #52c41a;
}

/* 复制按钮样式 */
.copy-btn {
  margin-left: 8px;
  cursor: pointer;
}

.copy-btn:hover {
  color: #1890ff;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .ant-table {
    font-size: 12px;
  }

  .ant-btn {
    font-size: 12px;
    padding: 0 8px;
  }

  .ant-card-head-title {
    font-size: 14px;
  }

  .ant-statistic-title {
    font-size: 12px;
  }

  .ant-statistic-content {
    font-size: 20px !important;
  }

  .ant-modal-title {
    font-size: 16px;
  }

  .ant-list-item-meta-title {
    font-size: 14px;
  }

  .ant-list-item-meta-description {
    font-size: 12px;
  }

  /* 调整表单在移动设备上的样式 */
  .ant-form-item-label {
    padding-bottom: 4px;
  }

  .ant-form-item {
    margin-bottom: 16px;
  }

  /* 调整抽屉菜单样式 */
  .ant-drawer-title {
    font-size: 16px;
  }

  /* 调整移动设备上的内容间距 */
  .ant-layout-content {
    padding: 12px !important;
  }
}

/* 超小屏幕设备 */
@media (max-width: 480px) {
  .ant-card-body {
    padding: 12px;
  }

  .ant-table {
    font-size: 11px;
  }

  .ant-btn {
    font-size: 11px;
    padding: 0 4px;
  }

  .ant-statistic-content {
    font-size: 18px !important;
  }

  /* 在超小屏幕上隐藏某些表格列 */
  .ant-table-cell-ellipsis {
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* 调整移动设备上的内容间距 */
  .ant-layout-content {
    padding: 8px !important;
    margin: 8px !important;
  }

  /* 调整列表项样式 */
  .ant-list-item {
    padding: 8px 12px;
  }

  /* 调整头部样式 */
  .ant-layout-header {
    padding: 0 8px !important;
    height: 48px !important;
  }

  /* 调整按钮大小 */
  .ant-btn-sm {
    font-size: 10px;
    height: 22px;
    padding: 0px 4px;
  }
}
