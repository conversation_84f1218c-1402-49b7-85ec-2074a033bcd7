import { CalendarOutlined, CheckCircleOutlined, CloseCircleOutlined, CopyOutlined, DeleteOutlined, EditOutlined, LockOutlined, MailOutlined, ReloadOutlined, UserOutlined } from '@ant-design/icons';
import { Avatar, Button, Card, Col, Form, Input, List, message, Modal, Popconfirm, Row, Select, Space, Statistic, Switch, Table, Tag } from 'antd';
import React, { useEffect, useState } from 'react';
import { accountApi } from '../services/api';
import { copyToClipboard, formatTimestamp, getFullName, isAccountExpired, isMobile, isSmallMobile } from '../utils';

const AccountList = () => {
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [getAccountLoading, setGetAccountLoading] = useState(false);
  const [stats, setStats] = useState({
    total: 0,
    used: 0,
    available: 0
  });
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingAccount, setEditingAccount] = useState(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [mobile, setMobile] = useState(isMobile());

  // 监听窗口大小变化，更新移动设备状态
  useEffect(() => {
    const handleResize = () => {
      setMobile(isMobile());
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // 获取账号列表
  const fetchAccounts = async (page = pagination.current, pageSize = pagination.pageSize) => {
    try {
      setLoading(true);
      const response = await accountApi.getUserAccounts(page, pageSize);

      if (response.status === 'success') {
        setAccounts(response.accounts || []);

        // 更新分页信息
        setPagination({
          current: response.page,
          pageSize: response.per_page,
          total: response.total
        });

        // 计算统计数据
        const used = response.accounts.filter(acc => acc.is_used === 1).length;
        const available = response.accounts.length - used;

        setStats({
          total: response.total,
          used: used,
          available: available
        });
      } else {
        message.error(response.message || '获取账号列表失败');
      }
    } catch (error) {
      console.error('获取账号列表失败:', error);
      message.error('获取账号列表失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 处理表格分页变化
  const handleTableChange = (paginationInfo) => {
    fetchAccounts(paginationInfo.current, paginationInfo.pageSize);
  };

  // 获取新账号
  const getNewAccount = async () => {
    try {
      setGetAccountLoading(true);
      const response = await accountApi.getAccount();

      if (response.status === 'success') {
        message.success('获取新账号成功');
        // 显示账号详情
        setSelectedAccount(response.account);
        setModalVisible(true);
        // 刷新账号列表，回到第一页
        fetchAccounts(1, pagination.pageSize);
      } else {
        message.error(response.message || '获取新账号失败');
      }
    } catch (error) {
      console.error('获取新账号失败:', error);
      message.error('获取新账号失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setGetAccountLoading(false);
    }
  };

  // 更新账号状态
  const updateAccountStatus = async (accountId, isUsed) => {
    try {
      const response = await accountApi.updateAccountStatus(accountId, isUsed ? 1 : 0);

      if (response.status === 'success') {
        message.success('账号状态已更新');
        // 更新本地账号列表
        setAccounts(accounts.map(acc =>
          acc.id === accountId ? { ...acc, is_used: isUsed ? 1 : 0 } : acc
        ));

        // 更新统计数据
        const used = isUsed
          ? stats.used + 1
          : stats.used - 1;
        const available = stats.total - used;

        setStats({ ...stats, used, available });
      } else {
        message.error(response.message || '更新账号状态失败');
      }
    } catch (error) {
      console.error('更新账号状态失败:', error);
      message.error('更新账号状态失败: ' + (error.response?.data?.message || error.message));
    }
  };

  // 复制账号信息
  const copyAccountInfo = (account) => {
    const info = `邮箱: ${account.email}\n密码: ${account.password}\n姓名: ${getFullName(account.first_name, account.last_name)}\n过期时间: ${formatTimestamp(account.expire_time)}`;

    copyToClipboard(info)
      .then(() => {
        message.success('账号信息已复制到剪贴板');
      })
      .catch(() => {
        message.error('复制失败，请手动复制');
      });
  };

  // 复制单个字段
  const copyField = (text) => {
    copyToClipboard(text)
      .then(() => {
        message.success('已复制到剪贴板');
      })
      .catch(() => {
        message.error('复制失败，请手动复制');
      });
  };

  // 编辑账号
  const editAccount = (account) => {
    setEditingAccount({ ...account });
    setEditModalVisible(true);
  };

  // 保存编辑
  const saveEdit = async () => {
    try {
      // 调用 API 更新账号信息
      const response = await accountApi.updateAccount(editingAccount.id, editingAccount);

      if (response.status === 'success') {
        // 更新本地状态
        setAccounts(accounts.map(acc =>
          acc.id === editingAccount.id ? response.account : acc
        ));

        message.success('账号信息已更新');
        setEditModalVisible(false);
        setEditingAccount(null);
      } else {
        message.error(response.message || '更新账号失败');
      }
    } catch (error) {
      console.error('更新账号失败:', error);
      message.error('更新账号失败: ' + (error.response?.data?.message || error.message));
    }
  };

  // 登录到 Cursor
  const loginToCursor = () => {
    Modal.confirm({
      title: '登录现有 Cursor 账号',
      content: (
        <div>
          <p>请输入您要登录的 Cursor 账号邮箱：</p>
          <Input
            id="cursor-email-input"
            placeholder="请输入邮箱地址"
            style={{ marginTop: 8 }}
          />
        </div>
      ),
      onOk: async () => {
        const emailInput = document.getElementById('cursor-email-input');
        const email = emailInput?.value?.trim();

        if (!email) {
          message.error('请输入邮箱地址');
          return Promise.reject();
        }

        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
          message.error('请输入有效的邮箱地址');
          return Promise.reject();
        }

        try {
          message.loading('正在登录 Cursor 账号，请稍候...', 0);
          const response = await accountApi.loginCursor(email);
          message.destroy();

          if (response.status === 'success') {
            message.success('登录成功！');
            // 刷新账号列表
            fetchAccounts();
          } else {
            message.error(response.message || '登录失败');
          }
        } catch (error) {
          message.destroy();
          console.error('登录失败:', error);
          message.error('登录失败: ' + (error.response?.data?.message || error.message));
        }
      },
      okText: '登录',
      cancelText: '取消',
    });
  };

  // 删除账号（逻辑删除）
  const deleteAccount = async (accountId) => {
    try {
      const response = await accountApi.deleteAccount(accountId);

      if (response.status === 'success') {
        message.success('账号已删除');
        // 从列表中移除该账号
        setAccounts(accounts.filter(acc => acc.id !== accountId));

        // 更新统计数据
        setStats({
          ...stats,
          total: stats.total - 1,
          used: accounts.find(acc => acc.id === accountId)?.is_used === 1
            ? stats.used - 1
            : stats.used,
          available: accounts.find(acc => acc.id === accountId)?.is_used === 0
            ? stats.available - 1
            : stats.available
        });
      } else {
        message.error(response.message || '删除账号失败');
      }
    } catch (error) {
      console.error('删除账号失败:', error);
      message.error('删除账号失败: ' + (error.response?.data?.message || error.message));
    }
  };

  // 初始化时获取账号列表
  useEffect(() => {
    fetchAccounts();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      render: (text) => (
        <Space>
          <span>{text}</span>
          <Button
            type="text"
            icon={<CopyOutlined />}
            onClick={() => copyField(text)}
            size="small"
          />
        </Space>
      ),
    },
    {
      title: '密码',
      dataIndex: 'password',
      key: 'password',
      render: (text) => (
        <Space>
          <span>••••••••</span>
          <Button
            type="text"
            icon={<CopyOutlined />}
            onClick={() => copyField(text)}
            size="small"
          />
        </Space>
      ),
    },
    {
      title: '姓名',
      key: 'name',
      render: (_, record) => getFullName(record.first_name, record.last_name),
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      render: (text) => formatTimestamp(text),
    },
    {
      title: '过期时间',
      dataIndex: 'expire_time',
      key: 'expire_time',
      render: (text) => formatTimestamp(text),
    },
    {
      title: '使用限制',
      dataIndex: 'usage_limit',
      key: 'usage_limit',
      render: (text) => (
        <Tag color={text === 'unlimited' ? 'gold' : 'blue'}>
          {text || '未知'}
        </Tag>
      ),
    },
    {
      title: '账号类型',
      dataIndex: 'account_type',
      key: 'account_type',
      render: (text) => {
        const colorMap = {
          'free': 'default',
          'pro': 'blue',
          'premium': 'gold'
        };
        const labelMap = {
          'free': '免费版',
          'pro': '专业版',
          'premium': '高级版'
        };
        return (
          <Tag color={colorMap[text] || 'default'}>
            {labelMap[text] || text || '未知'}
          </Tag>
        );
      },
    },
    {
      title: '账号状态',
      dataIndex: 'status',
      key: 'account_status',
      render: (text) => {
        const colorMap = {
          'active': 'green',
          'inactive': 'orange',
          'suspended': 'red'
        };
        const labelMap = {
          'active': '正常',
          'inactive': '未激活',
          'suspended': '已暂停'
        };
        return (
          <Tag color={colorMap[text] || 'default'}>
            {labelMap[text] || text || '未知'}
          </Tag>
        );
      },
    },
    {
      title: '使用状态',
      key: 'usage_status',
      render: (_, record) => {
        const expired = isAccountExpired(record.expire_time);
        if (expired) {
          return <Tag color="red">已过期</Tag>;
        }
        return (
          <Switch
            checkedChildren="已用"
            unCheckedChildren="未用"
            checked={record.is_used === 1}
            onChange={(checked) => updateAccountStatus(record.id, checked)}
          />
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            type="primary"
            size="small"
            icon={<CopyOutlined />}
            onClick={() => copyAccountInfo(record)}
          >
            复制
          </Button>
          <Button
            type="default"
            size="small"
            icon={<EditOutlined />}
            onClick={() => editAccount(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此账号吗？"
            description="删除后将无法在此页面查看，但管理员仍可查看"
            onConfirm={() => deleteAccount(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="primary"
              danger
              size="small"
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 移动设备上的列表项渲染
  const renderMobileAccountItem = (account) => {
    const expired = isAccountExpired(account.expire_time);

    return (
      <List.Item
        key={account.id}
        actions={[
          <Button
            type="text"
            icon={<CopyOutlined />}
            onClick={() => copyAccountInfo(account)}
          />,
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => editAccount(account)}
          />,
          <Popconfirm
            title="确定要删除此账号吗？"
            description="删除后将无法在此页面查看，但管理员仍可查看"
            onConfirm={() => deleteAccount(account.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        ]}
      >
        <List.Item.Meta
          avatar={<Avatar icon={<UserOutlined />} />}
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>{getFullName(account.first_name, account.last_name)}</span>
              {expired ? (
                <Tag color="red">已过期</Tag>
              ) : (
                <Switch
                  checkedChildren="已用"
                  unCheckedChildren="未用"
                  checked={account.is_used === 1}
                  onChange={(checked) => updateAccountStatus(account.id, checked)}
                  size="small"
                />
              )}
            </div>
          }
          description={
            <div>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
                <MailOutlined style={{ marginRight: 8 }} />
                <span style={{ flex: 1 }}>{account.email}</span>
                <Button
                  type="text"
                  size="small"
                  icon={<CopyOutlined />}
                  onClick={() => copyField(account.email)}
                />
              </div>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
                <LockOutlined style={{ marginRight: 8 }} />
                <span style={{ flex: 1 }}>••••••••</span>
                <Button
                  type="text"
                  size="small"
                  icon={<CopyOutlined />}
                  onClick={() => copyField(account.password)}
                />
              </div>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
                <CalendarOutlined style={{ marginRight: 8 }} />
                <span>{formatTimestamp(account.expire_time)}</span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
                <span style={{ marginRight: 8, fontSize: '12px', color: '#666' }}>限制:</span>
                <Tag size="small" color={account.usage_limit === 'unlimited' ? 'gold' : 'blue'}>
                  {account.usage_limit || '未知'}
                </Tag>
                <span style={{ marginLeft: 8, marginRight: 8, fontSize: '12px', color: '#666' }}>类型:</span>
                <Tag size="small" color={
                  account.account_type === 'premium' ? 'gold' :
                  account.account_type === 'pro' ? 'blue' : 'default'
                }>
                  {account.account_type === 'free' ? '免费版' :
                   account.account_type === 'pro' ? '专业版' :
                   account.account_type === 'premium' ? '高级版' :
                   account.account_type || '未知'}
                </Tag>
              </div>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span style={{ marginRight: 8, fontSize: '12px', color: '#666' }}>状态:</span>
                <Tag size="small" color={
                  account.status === 'active' ? 'green' :
                  account.status === 'inactive' ? 'orange' :
                  account.status === 'suspended' ? 'red' : 'default'
                }>
                  {account.status === 'active' ? '正常' :
                   account.status === 'inactive' ? '未激活' :
                   account.status === 'suspended' ? '已暂停' :
                   account.status || '未知'}
                </Tag>
              </div>
            </div>
          }
        />
      </List.Item>
    );
  };

  // 响应式统计卡片
  const renderStatCards = () => {
    const colSpan = mobile ? 24 : 8;
    const gutter = mobile ? [0, 16] : 16;

    return (
      <Row gutter={gutter} style={{ marginBottom: mobile ? 8 : 16 }}>
        <Col span={colSpan} style={{ marginBottom: mobile ? 16 : 0 }}>
          <Card>
            <Statistic
              title="总账号数"
              value={stats.total}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={colSpan} style={{ marginBottom: mobile ? 16 : 0 }}>
          <Card>
            <Statistic
              title="已使用账号"
              value={stats.used}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={colSpan}>
          <Card>
            <Statistic
              title="可用账号"
              value={stats.available}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  return (
    <div>
      {renderStatCards()}

      <Card
        title="账号管理"
        extra={
          <Space size={mobile ? 'small' : 'middle'}>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchAccounts}
              loading={loading}
              size={mobile ? 'small' : 'middle'}
            >
              {!isSmallMobile() && '刷新'}
            </Button>
            <Button
              type="primary"
              onClick={getNewAccount}
              loading={getAccountLoading}
              size={mobile ? 'small' : 'middle'}
            >
              {isSmallMobile() ? '新账号' : '注册账号'}
            </Button>
            <Button
              type="default"
              onClick={loginToCursor}
              size={mobile ? 'small' : 'middle'}
            >
              {isSmallMobile() ? '登录' : '登录 Cursor'}
            </Button>
          </Space>
        }
      >
        {mobile ? (
          <List
            itemLayout="vertical"
            dataSource={accounts}
            renderItem={renderMobileAccountItem}
            loading={loading}
            pagination={{
              ...pagination,
              size: 'small',
              onChange: (page, pageSize) => fetchAccounts(page, pageSize)
            }}
          />
        ) : (
          <Table
            columns={columns}
            dataSource={accounts}
            rowKey="id"
            loading={loading}
            pagination={pagination}
            onChange={handleTableChange}
            scroll={{ x: 'max-content' }}
          />
        )}
      </Card>

      {/* 账号详情弹窗 */}
      <Modal
        title="新账号详情"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button key="copy" type="primary" onClick={() => {
            if (selectedAccount) {
              copyAccountInfo(selectedAccount);
            }
          }}>
            复制账号信息
          </Button>,
          <Button key="close" onClick={() => setModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={mobile ? '95%' : 520}
        styles={{ body: { padding: mobile ? '16px' : '24px' } }}
      >
        {selectedAccount && (
          <div>
            {mobile ? (
              <List>
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<MailOutlined />} />}
                    title="邮箱"
                    description={
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <span style={{ flex: 1 }}>{selectedAccount.email}</span>
                        <Button
                          type="text"
                          size="small"
                          icon={<CopyOutlined />}
                          onClick={() => copyField(selectedAccount.email)}
                        />
                      </div>
                    }
                  />
                </List.Item>
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<LockOutlined />} />}
                    title="密码"
                    description={
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <span style={{ flex: 1 }}>{selectedAccount.password}</span>
                        <Button
                          type="text"
                          size="small"
                          icon={<CopyOutlined />}
                          onClick={() => copyField(selectedAccount.password)}
                        />
                      </div>
                    }
                  />
                </List.Item>
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<UserOutlined />} />}
                    title="姓名"
                    description={getFullName(selectedAccount.first_name, selectedAccount.last_name)}
                  />
                </List.Item>
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<CalendarOutlined />} />}
                    title="过期时间"
                    description={formatTimestamp(selectedAccount.expire_time)}
                  />
                </List.Item>
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar style={{ backgroundColor: '#1890ff' }}>限</Avatar>}
                    title="使用限制"
                    description={
                      <Tag color={selectedAccount.usage_limit === 'unlimited' ? 'gold' : 'blue'}>
                        {selectedAccount.usage_limit || '未知'}
                      </Tag>
                    }
                  />
                </List.Item>
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar style={{ backgroundColor: '#52c41a' }}>类</Avatar>}
                    title="账号类型"
                    description={
                      <Tag color={
                        selectedAccount.account_type === 'premium' ? 'gold' :
                        selectedAccount.account_type === 'pro' ? 'blue' : 'default'
                      }>
                        {selectedAccount.account_type === 'free' ? '免费版' :
                         selectedAccount.account_type === 'pro' ? '专业版' :
                         selectedAccount.account_type === 'premium' ? '高级版' :
                         selectedAccount.account_type || '未知'}
                      </Tag>
                    }
                  />
                </List.Item>
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar style={{ backgroundColor: '#fa8c16' }}>状</Avatar>}
                    title="账号状态"
                    description={
                      <Tag color={
                        selectedAccount.status === 'active' ? 'green' :
                        selectedAccount.status === 'inactive' ? 'orange' :
                        selectedAccount.status === 'suspended' ? 'red' : 'default'
                      }>
                        {selectedAccount.status === 'active' ? '正常' :
                         selectedAccount.status === 'inactive' ? '未激活' :
                         selectedAccount.status === 'suspended' ? '已暂停' :
                         selectedAccount.status || '未知'}
                      </Tag>
                    }
                  />
                </List.Item>
              </List>
            ) : (
              <div>
                <p><strong>邮箱:</strong> {selectedAccount.email} <Button type="text" size="small" icon={<CopyOutlined />} onClick={() => copyField(selectedAccount.email)} /></p>
                <p><strong>密码:</strong> {selectedAccount.password} <Button type="text" size="small" icon={<CopyOutlined />} onClick={() => copyField(selectedAccount.password)} /></p>
                <p><strong>姓名:</strong> {getFullName(selectedAccount.first_name, selectedAccount.last_name)}</p>
                <p><strong>过期时间:</strong> {formatTimestamp(selectedAccount.expire_time)}</p>
                <p><strong>使用限制:</strong>
                  <Tag color={selectedAccount.usage_limit === 'unlimited' ? 'gold' : 'blue'} style={{ marginLeft: 8 }}>
                    {selectedAccount.usage_limit || '未知'}
                  </Tag>
                </p>
                <p><strong>账号类型:</strong>
                  <Tag color={
                    selectedAccount.account_type === 'premium' ? 'gold' :
                    selectedAccount.account_type === 'pro' ? 'blue' : 'default'
                  } style={{ marginLeft: 8 }}>
                    {selectedAccount.account_type === 'free' ? '免费版' :
                     selectedAccount.account_type === 'pro' ? '专业版' :
                     selectedAccount.account_type === 'premium' ? '高级版' :
                     selectedAccount.account_type || '未知'}
                  </Tag>
                </p>
                <p><strong>账号状态:</strong>
                  <Tag color={
                    selectedAccount.status === 'active' ? 'green' :
                    selectedAccount.status === 'inactive' ? 'orange' :
                    selectedAccount.status === 'suspended' ? 'red' : 'default'
                  } style={{ marginLeft: 8 }}>
                    {selectedAccount.status === 'active' ? '正常' :
                     selectedAccount.status === 'inactive' ? '未激活' :
                     selectedAccount.status === 'suspended' ? '已暂停' :
                     selectedAccount.status || '未知'}
                  </Tag>
                </p>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 编辑账号弹窗 */}
      <Modal
        title="编辑账号信息"
        open={editModalVisible}
        onOk={saveEdit}
        onCancel={() => {
          setEditModalVisible(false);
          setEditingAccount(null);
        }}
        okText="保存"
        cancelText="取消"
      >
        {editingAccount && (
          <Form layout="vertical">
            <Form.Item label="姓">
              <Input
                value={editingAccount.first_name}
                onChange={(e) => setEditingAccount({ ...editingAccount, first_name: e.target.value })}
              />
            </Form.Item>
            <Form.Item label="名">
              <Input
                value={editingAccount.last_name}
                onChange={(e) => setEditingAccount({ ...editingAccount, last_name: e.target.value })}
              />
            </Form.Item>
            <Form.Item label="邮箱">
              <Input
                value={editingAccount.email}
                onChange={(e) => setEditingAccount({ ...editingAccount, email: e.target.value })}
              />
            </Form.Item>
            <Form.Item label="密码">
              <Input
                value={editingAccount.password}
                onChange={(e) => setEditingAccount({ ...editingAccount, password: e.target.value })}
              />
            </Form.Item>
            <Form.Item label="账号类型">
              <Select
                value={editingAccount.account_type}
                onChange={(value) => setEditingAccount({ ...editingAccount, account_type: value })}
              >
                <Select.Option value="free">免费版</Select.Option>
                <Select.Option value="pro">专业版</Select.Option>
                <Select.Option value="premium">高级版</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item label="账号状态">
              <Select
                value={editingAccount.status}
                onChange={(value) => setEditingAccount({ ...editingAccount, status: value })}
              >
                <Select.Option value="active">正常</Select.Option>
                <Select.Option value="inactive">未激活</Select.Option>
                <Select.Option value="suspended">已暂停</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item label="Access Token">
              <Input.TextArea
                value={editingAccount.accessToken}
                onChange={(e) => setEditingAccount({ ...editingAccount, accessToken: e.target.value })}
                placeholder="Cursor 访问令牌"
                rows={3}
              />
            </Form.Item>
          </Form>
        )}
      </Modal>
    </div>
  );
};

export default AccountList;
