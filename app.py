import os
import random
import string
from flask import Flask
from dotenv import load_dotenv

# 加载.env文件
load_dotenv()

# 导入自定义模块
from models import db
import auth
from db_utils import init_db
from views.api import api_bp
import urllib.parse
# 创建并配置 Flask 应用
def create_app():
    app = Flask(__name__)

    # 从环境变量获取数据库配置
    app.config['DB_HOST'] = os.getenv('DB_HOST', 'localhost')
    app.config['DB_PORT'] = int(os.getenv('DB_PORT', '3306'))
    app.config['DB_USER'] = os.getenv('DB_USER', 'root')
    app.config['DB_PASSWORD'] = os.getenv('DB_PASSWORD', 'root')
    app.config['DB_NAME'] = os.getenv('DB_NAME', 'cursor_accounts')

    # 设置 SQLAlchemy 数据库 URI
    # 对密码进行URL编码，避免特殊字符（如@）造成解析问题
    encoded_password = urllib.parse.quote_plus(app.config["DB_PASSWORD"])
    app.config['SQLALCHEMY_DATABASE_URI'] = f'mysql+pymysql://{app.config["DB_USER"]}:{encoded_password}@{app.config["DB_HOST"]}:{app.config["DB_PORT"]}/{app.config["DB_NAME"]}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

    # JWT密钥
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', ''.join(random.choices(string.ascii_letters + string.digits, k=32)))
    # Token有效期（天）
    app.config['TOKEN_EXPIRY_DAYS'] = int(os.getenv('TOKEN_EXPIRY_DAYS', '30'))

    # 初始化数据库
    db.init_app(app)

    # 设置认证模块的密钥和过期时间 - 修复直接设置模块变量
    auth.SECRET_KEY = app.config['SECRET_KEY']
    auth.TOKEN_EXPIRY_DAYS = app.config['TOKEN_EXPIRY_DAYS']

    # 注册蓝图
    app.register_blueprint(api_bp)

    # 添加主页路由
    @app.route('/')
    def index():
        return '''
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Cursor 账号管理系统</title>
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }

                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    padding: 20px;
                }

                .container {
                    max-width: 1000px;
                    margin: 0 auto;
                    background: rgba(255, 255, 255, 0.95);
                    border-radius: 20px;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                    overflow: hidden;
                    backdrop-filter: blur(10px);
                }

                .header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 40px 30px;
                    text-align: center;
                    position: relative;
                }

                .header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
                    opacity: 0.3;
                }

                .header h1 {
                    font-size: 2.5rem;
                    margin-bottom: 10px;
                    position: relative;
                    z-index: 1;
                }

                .header p {
                    font-size: 1.1rem;
                    opacity: 0.9;
                    position: relative;
                    z-index: 1;
                }

                .status-badge {
                    display: inline-flex;
                    align-items: center;
                    background: rgba(40, 167, 69, 0.2);
                    color: #28a745;
                    padding: 8px 16px;
                    border-radius: 25px;
                    font-weight: 600;
                    margin: 20px 0;
                    border: 2px solid rgba(40, 167, 69, 0.3);
                }

                .status-badge i {
                    margin-right: 8px;
                }

                .content {
                    padding: 40px 30px;
                }

                .section {
                    margin-bottom: 40px;
                }

                .section h3 {
                    color: #333;
                    font-size: 1.5rem;
                    margin-bottom: 20px;
                    display: flex;
                    align-items: center;
                }

                .section h3 i {
                    margin-right: 10px;
                    color: #667eea;
                }

                .api-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 20px;
                }

                .api-card {
                    background: white;
                    border: 1px solid #e9ecef;
                    border-radius: 12px;
                    padding: 20px;
                    transition: all 0.3s ease;
                    position: relative;
                    overflow: hidden;
                }

                .api-card::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 4px;
                    height: 100%;
                    background: linear-gradient(135deg, #667eea, #764ba2);
                    transition: width 0.3s ease;
                }

                .api-card:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
                }

                .api-card:hover::before {
                    width: 8px;
                }

                .method {
                    display: inline-block;
                    padding: 4px 12px;
                    border-radius: 20px;
                    font-size: 0.8rem;
                    font-weight: 700;
                    text-transform: uppercase;
                    margin-bottom: 10px;
                }

                .method.get {
                    background: #e3f2fd;
                    color: #1976d2;
                }

                .method.post {
                    background: #e8f5e8;
                    color: #388e3c;
                }

                .endpoint {
                    font-family: 'Courier New', monospace;
                    background: #f8f9fa;
                    padding: 8px 12px;
                    border-radius: 6px;
                    font-size: 0.9rem;
                    color: #495057;
                    margin: 10px 0;
                    border-left: 3px solid #667eea;
                }

                .description {
                    color: #666;
                    font-size: 0.9rem;
                    line-height: 1.5;
                }

                .features {
                    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                    border-radius: 12px;
                    padding: 30px;
                    margin: 30px 0;
                }

                .features h3 {
                    color: #333;
                    margin-bottom: 20px;
                }

                .feature-list {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 15px;
                }

                .feature-item {
                    display: flex;
                    align-items: center;
                    padding: 10px;
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
                }

                .feature-item i {
                    color: #667eea;
                    margin-right: 12px;
                    font-size: 1.2rem;
                }

                .footer {
                    text-align: center;
                    padding: 30px;
                    background: #f8f9fa;
                    color: #666;
                }

                .footer a {
                    color: #667eea;
                    text-decoration: none;
                    font-weight: 600;
                    transition: color 0.3s ease;
                }

                .footer a:hover {
                    color: #764ba2;
                }

                @media (max-width: 768px) {
                    .header h1 {
                        font-size: 2rem;
                    }

                    .api-grid {
                        grid-template-columns: 1fr;
                    }

                    .content {
                        padding: 20px;
                    }
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1><i class="fas fa-code"></i> Cursor 账号管理系统</h1>
                    <p>智能化的 Cursor 账号自动注册与管理平台</p>
                    <div class="status-badge">
                        <i class="fas fa-check-circle"></i>
                        服务运行正常
                    </div>
                </div>

                <div class="content">
                    <div class="section">
                        <h3><i class="fas fa-rocket"></i> 系统特性</h3>
                        <div class="features">
                            <div class="feature-list">
                                <div class="feature-item">
                                    <i class="fas fa-robot"></i>
                                    <span>全自动账号注册</span>
                                </div>
                                <div class="feature-item">
                                    <i class="fas fa-database"></i>
                                    <span>智能数据管理</span>
                                </div>
                                <div class="feature-item">
                                    <i class="fas fa-shield-alt"></i>
                                    <span>安全权限控制</span>
                                </div>
                                <div class="feature-item">
                                    <i class="fas fa-chart-line"></i>
                                    <span>实时状态监控</span>
                                </div>
                                <div class="feature-item">
                                    <i class="fas fa-cloud"></i>
                                    <span>云端部署支持</span>
                                </div>
                                <div class="feature-item">
                                    <i class="fas fa-mobile-alt"></i>
                                    <span>响应式设计</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="section">
                        <h3><i class="fas fa-plug"></i> API 接口</h3>
                        <div class="api-grid">
                            <div class="api-card">
                                <div class="method get">GET</div>
                                <div class="endpoint">/api/health</div>
                                <div class="description">系统健康检查，获取服务运行状态</div>
                            </div>

                            <div class="api-card">
                                <div class="method post">POST</div>
                                <div class="endpoint">/api/register</div>
                                <div class="description">用户注册接口，创建新的用户账户</div>
                            </div>

                            <div class="api-card">
                                <div class="method post">POST</div>
                                <div class="endpoint">/api/login</div>
                                <div class="description">用户登录验证，获取访问令牌</div>
                            </div>

                            <div class="api-card">
                                <div class="method get">GET</div>
                                <div class="endpoint">/api/cursor/register</div>
                                <div class="description">自动创建 Cursor 账号，包含使用限制、账号类型等详细信息 <span style="color: #dc3545;">*需要登录</span></div>
                            </div>

                            <div class="api-card">
                                <div class="method get">GET</div>
                                <div class="endpoint">/api/accounts</div>
                                <div class="description">获取用户的所有账号列表 <span style="color: #dc3545;">*需要登录</span></div>
                            </div>

                            <div class="api-card">
                                <div class="method get">GET</div>
                                <div class="endpoint">/api/admin/accounts</div>
                                <div class="description">管理员获取所有账号 <span style="color: #dc3545;">*需要管理员权限</span></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="footer">
                    <p>
                        <i class="fas fa-heart" style="color: #e74c3c;"></i>
                        快速测试：<a href="/api/health" target="_blank">API 健康检查</a>
                    </p>
                    <p style="margin-top: 10px; font-size: 0.9rem;">
                        Powered by Flask & Python |
                        <i class="fas fa-code-branch"></i>
                        开源项目
                    </p>
                </div>
            </div>
        </body>
        </html>
        '''

    return app

# 应用入口
if __name__ == '__main__':
    # 创建应用
    app = create_app()

    # 初始化数据库
    init_db(app)

    # 获取环境变量中的主机和端口，默认为0.0.0.0:8001
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', '8001'))

    # 启动应用
    app.run(host=host, port=port, debug=(os.getenv('DEBUG', 'False').lower() == 'true'))