import os
import random
import string
from flask import Flask
from dotenv import load_dotenv

# 加载.env文件
load_dotenv()

# 导入自定义模块
from models import db
import auth
from db_utils import init_db
from views.api import api_bp
import urllib.parse
# 创建并配置 Flask 应用
def create_app():
    app = Flask(__name__)

    # 从环境变量获取数据库配置
    app.config['DB_HOST'] = os.getenv('DB_HOST', 'localhost')
    app.config['DB_PORT'] = int(os.getenv('DB_PORT', '3306'))
    app.config['DB_USER'] = os.getenv('DB_USER', 'root')
    app.config['DB_PASSWORD'] = os.getenv('DB_PASSWORD', 'root')
    app.config['DB_NAME'] = os.getenv('DB_NAME', 'cursor_accounts')

    # 设置 SQLAlchemy 数据库 URI
    # 对密码进行URL编码，避免特殊字符（如@）造成解析问题
    encoded_password = urllib.parse.quote_plus(app.config["DB_PASSWORD"])
    app.config['SQLALCHEMY_DATABASE_URI'] = f'mysql+pymysql://{app.config["DB_USER"]}:{encoded_password}@{app.config["DB_HOST"]}:{app.config["DB_PORT"]}/{app.config["DB_NAME"]}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

    # JWT密钥
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', ''.join(random.choices(string.ascii_letters + string.digits, k=32)))
    # Token有效期（天）
    app.config['TOKEN_EXPIRY_DAYS'] = int(os.getenv('TOKEN_EXPIRY_DAYS', '30'))

    # 初始化数据库
    db.init_app(app)

    # 设置认证模块的密钥和过期时间 - 修复直接设置模块变量
    auth.SECRET_KEY = app.config['SECRET_KEY']
    auth.TOKEN_EXPIRY_DAYS = app.config['TOKEN_EXPIRY_DAYS']

    # 注册蓝图
    app.register_blueprint(api_bp)

    # 添加主页路由
    @app.route('/')
    def index():
        return '''
        <html>
        <head>
            <title>Cursor 账号管理系统</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
                .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                h1 { color: #333; text-align: center; }
                .api-list { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
                .api-item { margin: 10px 0; padding: 10px; background: white; border-left: 4px solid #007bff; }
                .method { font-weight: bold; color: #007bff; }
                .endpoint { font-family: monospace; background: #e9ecef; padding: 2px 6px; border-radius: 3px; }
                .status { color: #28a745; font-weight: bold; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🎯 Cursor 账号管理系统</h1>
                <p><span class="status">✅ 服务运行正常</span></p>
                <p>这是一个用于管理 Cursor 账号的 Web 服务，支持自动注册、存储账号信息、查看和修改账号状态。</p>

                <div class="api-list">
                    <h3>📋 可用的 API 接口：</h3>

                    <div class="api-item">
                        <span class="method">GET</span> <span class="endpoint">/api/health</span> - 健康检查
                    </div>

                    <div class="api-item">
                        <span class="method">POST</span> <span class="endpoint">/api/register</span> - 用户注册
                    </div>

                    <div class="api-item">
                        <span class="method">POST</span> <span class="endpoint">/api/login</span> - 用户登录
                    </div>

                    <div class="api-item">
                        <span class="method">GET</span> <span class="endpoint">/api/cursor/register</span> - 获取账号 (需要登录)
                    </div>

                    <div class="api-item">
                        <span class="method">GET</span> <span class="endpoint">/api/accounts</span> - 获取用户所有账号 (需要登录)
                    </div>

                    <div class="api-item">
                        <span class="method">GET</span> <span class="endpoint">/api/admin/accounts</span> - 管理员获取所有账号
                    </div>
                </div>

                <div style="margin-top: 30px; padding: 20px; background: #e7f3ff; border-radius: 5px;">
                    <h4>🔐 安全提示：</h4>
                    <p>• 请妥善保管您的登录凭据</p>
                    <p>• 建议定期更改密码</p>
                    <p>• 管理员功能需要特殊权限</p>
                </div>

                <div style="margin-top: 20px; text-align: center; color: #666;">
                    <p>访问 <a href="/api/health" target="_blank">/api/health</a> 测试 API 连接</p>
                </div>
            </div>
        </body>
        </html>
        '''

    return app

# 应用入口
if __name__ == '__main__':
    # 创建应用
    app = create_app()

    # 初始化数据库
    init_db(app)

    # 获取环境变量中的主机和端口，默认为0.0.0.0:8001
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', '8001'))

    # 启动应用
    app.run(host=host, port=port, debug=(os.getenv('DEBUG', 'False').lower() == 'true'))