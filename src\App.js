import { ConfigProvider } from 'antd';
import zhCN from 'antd/es/locale/zh_CN';
import React from 'react';
import { Navigate, Route, BrowserRouter as Router, Routes } from 'react-router-dom';
import './App.css';

// 页面组件
import AccountList from './pages/AccountList';
import Admin from './pages/Admin';
import Home from './pages/Home';
import Login from './pages/Login';
import Profile from './pages/Profile';

// 权限控制组件
import PrivateRoute from './components/PrivateRoute';

// 用户上下文
import { UserProvider } from './contexts/UserContext';

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <UserProvider>
        <Router>
          <Routes>
            {/* 公开路由 */}
            <Route path="/login" element={<Login />} />

            {/* 需要登录的路由 */}
            <Route path="/" element={
              <PrivateRoute>
                <Home />
              </PrivateRoute>
            } />

            <Route path="/accounts" element={
              <PrivateRoute>
                <AccountList />
              </PrivateRoute>
            } />

            <Route path="/profile" element={
              <PrivateRoute>
                <Profile />
              </PrivateRoute>
            } />

            {/* 管理员路由 */}
            <Route path="/admin" element={
              <PrivateRoute adminOnly={true}>
                <Admin />
              </PrivateRoute>
            } />

            {/* 默认重定向到首页 */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Router>
      </UserProvider>
    </ConfigProvider>
  );
}

export default App;
