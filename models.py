from datetime import datetime
from sqlalchemy import Column, Integer, <PERSON>, BigInteger, ForeignKey
from sqlalchemy.orm import relationship
from flask_sqlalchemy import SQLAlchemy

db = SQLAlchemy()

# 用户模型
class User(db.Model):
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String(80), unique=True, nullable=False)
    password_hash = Column(String(128), nullable=False)
    email = Column(String(100), unique=True, nullable=True)
    created_at = Column(BigInteger, nullable=False)
    last_login = Column(BigInteger, nullable=True)
    domain = Column(String(255), default='zoowayss.top')
    temp_email_address = Column(String(255), default='<EMAIL>',nullable=True)

    # 关联用户的账号
    accounts = relationship("Account", back_populates="user")

    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'created_at': self.created_at,
            'last_login': self.last_login,
            'domain': self.domain,
            'temp_email_address': self.temp_email_address
        }

    @staticmethod
    def hash_password(password):
        import hashlib
        # 简单哈希密码，生产环境应使用更安全的方法如bcrypt
        return hashlib.sha256(password.encode()).hexdigest()

    def verify_password(self, password):
        return self.password_hash == User.hash_password(password)

# 定义账号模型
class Account(db.Model):
    __tablename__ = 'accounts'

    id = Column(Integer, primary_key=True, autoincrement=True)
    email = Column(String(255), unique=True, nullable=False)
    password = Column(String(255), nullable=False)
    first_name = Column(String(100), nullable=True)
    last_name = Column(String(100), nullable=True)
    create_time = Column(BigInteger, nullable=False)
    expire_time = Column(BigInteger, nullable=False)
    is_used = Column(Integer, default=0)  # 0: 未使用, 1: 已使用
    is_deleted = Column(Integer, default=0)  # 0: 未删除, 1: 已删除
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True)  # 关联到用户
    accessToken = Column(String(500), nullable=True)  # Cursor 访问令牌
    usage = Column(String(50), nullable=True)  # 使用情况 (历史字段)
    days = Column(Integer, nullable=True)  # 天数 (历史字段)
    usage_limit = Column(String(50), nullable=True)  # 使用限制 (如: "2000 requests")
    account_type = Column(String(20), default='free')  # 账号类型 (free/pro/premium)
    status = Column(String(20), default='active')  # 账号状态 (active/inactive/suspended)

    # 关联用户
    user = relationship("User", back_populates="accounts")

    def to_dict(self):
        data = {
            'id': self.id,
            'email': self.email,
            'password': self.password,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'create_time': self.create_time,
            'expire_time': self.expire_time,
            'is_used': self.is_used,
            'is_deleted': self.is_deleted,
            'accessToken': self.accessToken,
            'temp_email_address': self.temp_email_address,
            'usage': self.usage,
            'days': self.days,
            'usage_limit': self.usage_limit,
            'account_type': self.account_type,
            'status': self.status,
            'expire_time_fmt': datetime.fromtimestamp(self.expire_time).strftime('%Y-%m-%d %H:%M:%S')
        }

        # 安全地添加user_id，如果有这个属性
        try:
            data['user_id'] = self.user_id
        except:
            data['user_id'] = None

        return data